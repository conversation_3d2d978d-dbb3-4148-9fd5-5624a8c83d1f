"""Web管理界面
基于FastAPI的现代化Web管理界面
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException, Request, Form, UploadFile, File, Header
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse, Response
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from .proxy_service import ProxyService
from .config import Config
from .models import ProviderConfig
from .hot_reload import HotReloadManager, ServiceReloader
from .auth_manager import AuthManager, UserRole


class ConfigRequest(BaseModel):
    """配置请求模型"""
    providers: Dict[str, List[Dict[str, Any]]]
    default_timeout: int = 30
    max_retries: int = 3
    health_check_interval: int = 300
    enable_monitoring: bool = True
    log_level: str = "INFO"


class ProviderRequest(BaseModel):
    """提供商请求模型"""
    name: str
    api_key: str
    config: Dict[str, Any]


class ChatCompletionRequest(BaseModel):
    """聊天补全请求模型"""
    model: str
    messages: List[Dict[str, str]]
    temperature: Optional[float] = 1.0
    max_tokens: Optional[int] = None
    top_p: Optional[float] = 1.0
    frequency_penalty: Optional[float] = 0.0
    presence_penalty: Optional[float] = 0.0
    stream: Optional[bool] = False


class ProxyRequest(BaseModel):
    """通用代理请求模型"""
    provider: str
    endpoint: str
    data: Dict[str, Any] = {}


class WebApp:
    """Web应用管理器"""
    
    def __init__(self, proxy_service: Optional[ProxyService] = None, enable_hot_reload: bool = True):
        self.app = FastAPI(
            title="AI代理服务",
            description="AI API代理服务 - 支持OpenAI、OpenRouter等多种提供商",
            version="1.0.0"
        )

        # 添加CORS支持
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # 生产环境应该限制具体域名
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        self.proxy_service = proxy_service
        self.config_file = "config.json"
        self.enable_hot_reload = enable_hot_reload

        # 初始化热重载管理器
        if enable_hot_reload:
            self.hot_reload_manager = HotReloadManager(self.config_file)
            self.service_reloader = ServiceReloader(self._create_service_from_config)
            self.hot_reload_manager.add_reload_callback(self._on_config_reload)
        else:
            self.hot_reload_manager = None
            self.service_reloader = None

        # 初始化认证管理器
        self.auth_manager = AuthManager("auth_config.json")

        # 创建必要的目录
        self._create_directories()

        # 设置模板和静态文件
        self.templates = Jinja2Templates(directory="api_proxy/templates")

        # 挂载静态文件
        self.app.mount("/static", StaticFiles(directory="api_proxy/static"), name="static")

        # 注册路由
        self._register_routes()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = ["api_proxy/templates", "api_proxy/static", "logs"]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

    def _create_service_from_config(self, config: Config) -> ProxyService:
        """从配置创建服务实例"""
        return ProxyService(config)

    def _on_config_reload(self, new_config: Config):
        """配置重载回调"""
        try:
            if self.service_reloader:
                self.service_reloader.reload_service(new_config)
                # 更新当前服务引用
                self.proxy_service = self.service_reloader.get_service()
        except Exception as e:
            print(f"服务重载失败: {e}")

    def start_hot_reload(self):
        """启动热重载监控"""
        if self.hot_reload_manager and not self.hot_reload_manager.is_monitoring:
            self.hot_reload_manager.start_monitoring()

    def stop_hot_reload(self):
        """停止热重载监控"""
        if self.hot_reload_manager and self.hot_reload_manager.is_monitoring:
            self.hot_reload_manager.stop_monitoring()

    def _verify_api_key(self, authorization: str):
        """验证API密钥"""
        if not authorization:
            raise HTTPException(status_code=401, detail="Missing authorization header")

        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization format")

        token = authorization[7:]  # 移除 "Bearer " 前缀
        api_key = self.auth_manager.verify_api_key(token)

        if not api_key:
            raise HTTPException(status_code=401, detail="Invalid API key")

        # 检查速率限制
        if not self.auth_manager.check_rate_limit(api_key):
            raise HTTPException(status_code=429, detail="Rate limit exceeded")

        return api_key
    
    def _register_routes(self):
        """注册所有路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def main_app(request: Request):
            """单页面应用主页"""
            response = self.templates.TemplateResponse("base.html", {
                "request": request,
                "title": "AI代理服务"
            })
            # 设置CSP策略，为Bootstrap添加必要的权限
            response.headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';"
            return response

        @self.app.get("/.well-known/appspecific/com.chrome.devtools.json")
        async def chrome_devtools_config():
            """Chrome开发者工具配置端点"""
            return {
                "version": "1.0",
                "name": "AI代理服务",
                "description": "AI代理服务管理界面",
                "devtools_frontend_url": "/devtools/inspector.html?ws=localhost:8000/devtools/page/"
            }

        @self.app.get("/favicon.ico")
        async def favicon():
            """网站图标"""
            return Response(status_code=204)  # 返回空响应，避免404错误

        @self.app.get("/robots.txt")
        async def robots_txt():
            """搜索引擎爬虫配置"""
            return Response(
                content="User-agent: *\nDisallow: /",
                media_type="text/plain"
            )

        @self.app.get("/manifest.json")
        async def manifest():
            """Web应用清单"""
            return {
                "name": "AI代理服务",
                "short_name": "AI Proxy",
                "description": "AI代理服务管理界面",
                "start_url": "/",
                "display": "standalone",
                "background_color": "#ffffff",
                "theme_color": "#007bff",
                "icons": []
            }
        
        # 配置页面路由已移除，使用单页面应用的 /api/config-content
        
        @self.app.post("/api/config")
        async def save_config(config: ConfigRequest):
            """保存配置"""
            try:
                # 验证配置
                providers = {}
                for provider_type, provider_configs in config.providers.items():
                    provider_list = []
                    # 只处理非空的配置列表
                    if provider_configs:
                        for i, pc in enumerate(provider_configs):
                            try:
                                provider_list.append(ProviderConfig(
                                    name=pc["name"],
                                    api_key=pc["api_key"],
                                    config=pc.get("config", {})
                                ))
                            except Exception as e:
                                raise ValueError(f"提供商 {provider_type} 第 {i+1} 个实例配置错误: {str(e)}")
                    providers[provider_type] = provider_list
                
                # 创建配置对象
                new_config = Config(
                    providers=providers,
                    default_timeout=config.default_timeout,
                    max_retries=config.max_retries,
                    health_check_interval=config.health_check_interval,
                    enable_monitoring=config.enable_monitoring,
                    log_level=config.log_level
                )
                
                # 保存到文件
                config_dict = {
                    "providers": config.providers,
                    "default_timeout": config.default_timeout,
                    "max_retries": config.max_retries,
                    "health_check_interval": config.health_check_interval,
                    "enable_monitoring": config.enable_monitoring,
                    "log_level": config.log_level
                }
                
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
                
                # 使用热重载或直接重新加载服务
                if self.enable_hot_reload and self.service_reloader:
                    self.service_reloader.reload_service(new_config)
                    self.proxy_service = self.service_reloader.get_service()
                    message = "配置已保存并热重载服务"
                else:
                    self.proxy_service = ProxyService(new_config)
                    message = "配置已保存并重新加载服务"

                return {"status": "success", "message": message}
                
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"配置保存失败: {str(e)}")
        

        @self.app.get("/api/health")
        async def get_health(force_check: bool = False):
            """获取健康状态

            Args:
                force_check: 是否强制执行健康检查，默认使用缓存
            """
            if not self.proxy_service:
                return {"error": "服务未启动"}

            # 只有在强制检查或缓存过期时才执行健康检查
            if force_check:
                self.proxy_service.health_checker.check_all(self.proxy_service.providers)

            healthy_providers = self.proxy_service.health_checker.get_healthy_providers(
                self.proxy_service.providers
            )

            # 计算提供商实例总数
            total_instances = sum(len(provider_list) for provider_list in self.proxy_service.providers.values())

            return {
                "healthy_providers": list(healthy_providers.keys()),
                "total_providers": total_instances,  # 返回实例总数而不是提供商类型数
                "provider_types": len(self.proxy_service.providers),  # 提供商类型数
                "status": "healthy" if healthy_providers else "unhealthy",
                "cached": not force_check
            }

        @self.app.get("/api/health/detailed")
        async def get_detailed_health():
            """获取详细健康状态（强制检查）"""
            return await get_health(force_check=True)

        @self.app.get("/api/stats")
        async def get_stats():
            """获取详细统计信息"""
            try:
                print(f"[DEBUG] 获取统计信息请求")
                print(f"[DEBUG] proxy_service存在: {self.proxy_service is not None}")

                if not self.proxy_service:
                    print(f"[DEBUG] 服务未启动")
                    return {"status": "error", "error": "服务未启动"}

                print(f"[DEBUG] monitor存在: {hasattr(self.proxy_service, 'monitor')}")
                if hasattr(self.proxy_service, 'monitor'):
                    print(f"[DEBUG] monitor类型: {type(self.proxy_service.monitor)}")

                stats = self.proxy_service.monitor.get_stats()
                print(f"[DEBUG] 获取到统计数据: {len(stats)} 个条目")

                file_info = self.proxy_service.monitor.get_stats_file_info()
                print(f"[DEBUG] 获取到文件信息: {file_info}")

                result = {
                    "status": "success",
                    "data": stats,
                    "file_info": file_info
                }
                print(f"[DEBUG] 返回结果状态: {result['status']}")
                return result

            except Exception as e:
                print(f"[ERROR] 获取统计信息失败: {e}")
                import traceback
                traceback.print_exc()
                return {
                    "status": "error",
                    "error": f"获取统计信息失败: {str(e)}"
                }

        @self.app.post("/api/stats/save")
        async def save_stats():
            """手动保存统计信息"""
            try:
                if not self.proxy_service:
                    return {"status": "error", "error": "服务未启动"}

                self.proxy_service.monitor.save_now()
                file_info = self.proxy_service.monitor.get_stats_file_info()
                return {
                    "status": "success",
                    "message": "统计数据已保存",
                    "file_info": file_info
                }
            except Exception as e:
                print(f"保存统计信息失败: {e}")
                return {"status": "error", "message": f"保存失败: {str(e)}"}

        @self.app.post("/api/stats/reset")
        async def reset_stats(provider: str = None):
            """重置统计信息

            Args:
                provider: 可选的提供商名称，如果不提供则重置所有统计
            """
            try:
                if not self.proxy_service:
                    return {"status": "error", "error": "服务未启动"}

                self.proxy_service.monitor.reset_stats(provider)
                message = f"已重置{provider}的统计信息" if provider else "已重置所有统计信息"
                return {"status": "success", "message": message}
            except Exception as e:
                print(f"重置统计信息失败: {e}")
                return {"status": "error", "message": f"重置失败: {str(e)}"}

        @self.app.post("/api/reload")
        async def manual_reload():
            """手动触发配置重载"""
            if not self.enable_hot_reload or not self.hot_reload_manager:
                return {"error": "热重载功能未启用"}

            try:
                success = self.hot_reload_manager.manual_reload()
                if success:
                    return {"status": "success", "message": "配置重载成功"}
                else:
                    return {"status": "error", "message": "配置重载失败"}
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"重载失败: {str(e)}")

        @self.app.get("/api/reload/status")
        async def get_reload_status():
            """获取热重载状态"""
            if not self.enable_hot_reload:
                return {"hot_reload_enabled": False}

            status = {
                "hot_reload_enabled": True,
                "monitoring": False,
                "reload_history": [],
                "watchdog_available": False
            }

            if self.hot_reload_manager:
                status.update(self.hot_reload_manager.get_status())

            if self.service_reloader:
                status["reload_history"] = self.service_reloader.get_reload_history()

            return status

        @self.app.get("/api/service/status")
        async def get_service_status():
            """获取服务详细状态"""
            if not self.proxy_service:
                return {"error": "服务未启动"}

            return self.proxy_service.get_service_status()

        # API信息页面路由已移除，使用单页面应用的 /api/api-info-content

        # ==================== 认证管理端点 ====================

        # 认证管理页面路由已移除，使用单页面应用的 /api/auth-content

        @self.app.get("/api/auth/keys")
        async def list_api_keys():
            """列出API密钥"""
            return {"keys": self.auth_manager.get_api_keys_info()}

        @self.app.post("/api/auth/keys")
        async def create_api_key(
            name: str = Form(...),
            role: str = Form(...),
            allowed_models: str = Form(...),
            allowed_providers: str = Form(...),
            rate_limit: int = Form(100),
            expires_days: Optional[int] = Form(None)
        ):
            """创建API密钥"""
            try:
                # 解析允许的模型和提供商
                models = [m.strip() for m in allowed_models.split(",") if m.strip()]
                providers = [p.strip() for p in allowed_providers.split(",") if p.strip()]

                # 创建密钥
                new_key = self.auth_manager.create_api_key(
                    name=name,
                    role=UserRole(role),
                    allowed_models=models,
                    allowed_providers=providers,
                    rate_limit=rate_limit,
                    expires_days=expires_days
                )

                return {"status": "success", "key": new_key, "message": "API密钥创建成功"}

            except Exception as e:
                raise HTTPException(status_code=400, detail=f"创建API密钥失败: {str(e)}")

        @self.app.post("/api/auth/keys/{key_id}/deactivate")
        async def deactivate_api_key(key_id: str):
            """停用API密钥"""
            success = self.auth_manager.deactivate_api_key(key_id)
            if success:
                return {"status": "success", "message": "API密钥已停用"}
            else:
                raise HTTPException(status_code=404, detail="API密钥不存在")

        @self.app.post("/api/auth/keys/{key_id}/activate")
        async def activate_api_key(key_id: str):
            """激活API密钥"""
            success = self.auth_manager.activate_api_key(key_id)
            if success:
                return {"status": "success", "message": "API密钥已激活"}
            else:
                raise HTTPException(status_code=404, detail="API密钥不存在")

        @self.app.delete("/api/auth/keys/{key_id}")
        async def delete_api_key(key_id: str):
            """删除API密钥"""
            success = self.auth_manager.delete_api_key(key_id)
            if success:
                return {"status": "success", "message": "API密钥已删除"}
            else:
                raise HTTPException(status_code=404, detail="API密钥不存在")

        @self.app.put("/api/auth/keys/{key_id}")
        async def update_api_key(
            key_id: str,
            name: str = Form(None),
            allowed_models: str = Form(None),
            allowed_providers: str = Form(None),
            rate_limit: int = Form(None)
        ):
            """更新API密钥"""
            try:
                # 解析允许的模型和提供商
                models = None
                providers = None
                if allowed_models:
                    models = [m.strip() for m in allowed_models.split(",") if m.strip()]
                if allowed_providers:
                    providers = [p.strip() for p in allowed_providers.split(",") if p.strip()]

                success = self.auth_manager.update_api_key(
                    key_id=key_id,
                    name=name,
                    allowed_models=models,
                    allowed_providers=providers,
                    rate_limit=rate_limit
                )

                if success:
                    return {"status": "success", "message": "API密钥更新成功"}
                else:
                    raise HTTPException(status_code=404, detail="API密钥不存在")

            except Exception as e:
                raise HTTPException(status_code=400, detail=f"更新API密钥失败: {str(e)}")

        @self.app.get("/api/auth/models")
        async def list_model_mappings():
            """列出模型映射"""
            mappings = []
            for mapping in self.auth_manager.model_mappings.values():
                mappings.append({
                    "public_name": mapping.public_name,
                    "provider": mapping.provider,
                    "real_model": mapping.real_model,
                    "description": mapping.description,
                    "is_active": mapping.is_active,
                    "price_multiplier": mapping.price_multiplier
                })
            return {"mappings": mappings}

        @self.app.post("/api/auth/models")
        async def create_model_mapping(
            public_name: str = Form(...),
            provider: str = Form(...),
            real_model: str = Form(...),
            description: str = Form(...),
            price_multiplier: float = Form(1.0)
        ):
            """创建模型映射"""
            try:
                self.auth_manager.add_model_mapping(
                    public_name=public_name,
                    provider=provider,
                    real_model=real_model,
                    description=description,
                    price_multiplier=price_multiplier
                )
                return {"status": "success", "message": "模型映射创建成功"}

            except Exception as e:
                raise HTTPException(status_code=400, detail=f"创建模型映射失败: {str(e)}")

        @self.app.delete("/api/auth/models/{public_name}")
        async def delete_model_mapping(public_name: str):
            """删除模型映射"""
            success = self.auth_manager.delete_model_mapping(public_name)
            if success:
                return {"status": "success", "message": "模型映射已删除"}
            else:
                raise HTTPException(status_code=404, detail="模型映射不存在")

        @self.app.put("/api/auth/models/{public_name}")
        async def update_model_mapping(
            public_name: str,
            provider: str = Form(None),
            real_model: str = Form(None),
            description: str = Form(None),
            price_multiplier: float = Form(None),
            is_active: bool = Form(None)
        ):
            """更新模型映射"""
            try:
                success = self.auth_manager.update_model_mapping(
                    public_name=public_name,
                    provider=provider,
                    real_model=real_model,
                    description=description,
                    price_multiplier=price_multiplier,
                    is_active=is_active
                )

                if success:
                    return {"status": "success", "message": "模型映射更新成功"}
                else:
                    raise HTTPException(status_code=404, detail="模型映射不存在")

            except Exception as e:
                raise HTTPException(status_code=400, detail=f"更新模型映射失败: {str(e)}")
        
        @self.app.get("/api/logs")
        async def get_logs(lines: int = 100):
            """获取日志"""
            try:
                log_file = "logs/app.log"
                if not os.path.exists(log_file):
                    return {"logs": [], "message": "日志文件不存在"}
                
                with open(log_file, 'r', encoding='utf-8') as f:
                    all_lines = f.readlines()
                    recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                return {"logs": [line.strip() for line in recent_lines]}
                
            except Exception as e:
                return {"error": f"读取日志失败: {str(e)}"}
        
        @self.app.post("/api/test")
        async def test_api(provider_type: str = Form(...), endpoint: str = Form(...)):
            """测试API调用"""
            if not self.proxy_service:
                raise HTTPException(status_code=400, detail="服务未启动")
            
            try:
                # 这里只是模拟测试，实际调用需要真实的API密钥
                result = {
                    "status": "success",
                    "message": f"模拟调用 {provider_type}/{endpoint} 成功",
                    "timestamp": datetime.now().isoformat()
                }
                return result
                
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"API测试失败: {str(e)}")

        # ==================== API中转端点 ====================

        @self.app.post("/v1/chat/completions")
        async def chat_completions(
            request: ChatCompletionRequest,
            authorization: str = Header(None)
        ):
            """OpenAI兼容的聊天补全API"""
            if not self.proxy_service:
                raise HTTPException(status_code=503, detail="代理服务未启动")

            try:
                # 验证API密钥
                api_key = self._verify_api_key(authorization)

                # 获取模型映射
                model_mapping = self.auth_manager.get_model_mapping(request.model)
                if not model_mapping:
                    raise HTTPException(status_code=400, detail=f"Model '{request.model}' not found")

                # 检查模型访问权限
                if not self.auth_manager.can_access_model(api_key, request.model):
                    raise HTTPException(status_code=403, detail=f"Access denied for model '{request.model}'")

                # 检查提供商访问权限
                if not self.auth_manager.can_access_provider(api_key, model_mapping.provider):
                    raise HTTPException(status_code=403, detail=f"Access denied for provider '{model_mapping.provider}'")

                # 调用代理服务（使用映射后的真实模型）
                response = self.proxy_service.call(
                    model_mapping.provider,
                    "chat/completions",
                    model=model_mapping.real_model,
                    messages=request.messages,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens,
                    top_p=request.top_p,
                    frequency_penalty=request.frequency_penalty,
                    presence_penalty=request.presence_penalty,
                    stream=request.stream
                )

                return response

            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"API调用失败: {str(e)}")

        @self.app.post("/api/proxy")
        async def proxy_request(
            request: ProxyRequest,
            authorization: str = Header(None)
        ):
            """通用代理API端点"""
            if not self.proxy_service:
                raise HTTPException(status_code=503, detail="代理服务未启动")

            try:
                # 验证授权（可选）
                if authorization and not authorization.startswith("Bearer "):
                    raise HTTPException(status_code=401, detail="Invalid authorization header")

                # 调用代理服务
                response = self.proxy_service.call(
                    request.provider,
                    request.endpoint,
                    **request.data
                )

                return response

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"代理调用失败: {str(e)}")

        @self.app.get("/v1/models")
        async def list_models(authorization: str = Header(None)):
            """列出可用模型（OpenAI兼容）"""
            try:
                # 如果没有代理服务，返回基础模型列表
                if not self.proxy_service:
                    basic_models = self.auth_manager.list_public_models()
                    return {"object": "list", "data": basic_models}

                # 如果没有认证头，返回公开模型列表
                if not authorization:
                    all_models = self.auth_manager.list_public_models()
                    return {"object": "list", "data": all_models}

                # 验证API密钥
                api_key = self._verify_api_key(authorization)

                # 获取用户可访问的模型
                all_models = self.auth_manager.list_public_models()
                accessible_models = []

                for model in all_models:
                    if self.auth_manager.can_access_model(api_key, model["id"]):
                        accessible_models.append(model)

                return {"object": "list", "data": accessible_models}

            except HTTPException:
                raise
            except Exception as e:
                # 如果出错，至少返回基础模型列表
                try:
                    basic_models = self.auth_manager.list_public_models()
                    return {"object": "list", "data": basic_models}
                except:
                    return {"object": "list", "data": []}

        @self.app.get("/api/providers")
        async def list_providers():
            """列出可用的提供商"""
            if not self.proxy_service:
                raise HTTPException(status_code=503, detail="代理服务未启动")

            providers = []
            for provider_type, provider_list in self.proxy_service.providers.items():
                providers.append({
                    "type": provider_type,
                    "count": len(provider_list),
                    "status": "active"
                })

            return {"providers": providers}

        # ==================== 单页面应用内容端点 ====================

        @self.app.get("/api/dashboard-content")
        async def get_dashboard_content(request: Request):
            """获取仪表板内容"""
            stats = {}
            if self.proxy_service:
                stats = self.proxy_service.monitor.get_stats()

            # 渲染仪表板内容（不包含基础模板）
            dashboard_html = """
            <!-- API服务信息快速访问 -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4 border-left-info">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-info">
                                <i class="fas fa-globe"></i> 对外API服务
                            </h6>
                            <button class="btn btn-info btn-sm" data-page="api-info">
                                <i class="fas fa-external-link-alt"></i> 查看详情
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            服务地址
                                        </div>
                                        <div class="h6 mb-0 font-weight-bold text-gray-800" id="serviceUrl">
                                            """ + str(request.url.scheme) + "://" + str(request.url.netloc) + """
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            可用模型
                                        </div>
                                        <div class="h6 mb-0 font-weight-bold text-gray-800" id="modelCount">
                                            """ + str(len(self.auth_manager.list_public_models())) + """ 个
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            提供商实例
                                        </div>
                                        <div class="h6 mb-0 font-weight-bold text-gray-800" id="providerInstanceCount">
                                            """ + str(self._get_provider_instance_count()) + """ 个
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            API密钥
                                        </div>
                                        <div class="h6 mb-0 font-weight-bold text-gray-800" id="keyCount">
                                            """ + str(len([k for k in self.auth_manager.get_api_keys_info() if k['is_active']])) + """ 个激活
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="btn-group w-100" role="group">
                                        <button class="btn btn-outline-info" data-page="api-info">
                                            <i class="fas fa-info-circle"></i> API信息
                                        </button>
                                        <button class="btn btn-outline-warning" data-page="auth">
                                            <i class="fas fa-key"></i> 管理密钥
                                        </button>
                                        <button class="btn btn-outline-success" data-action="copyApiUrl">
                                            <i class="fas fa-copy"></i> 复制地址
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 状态卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">服务状态</div>
                                    <div class="h5 mb-0 font-weight-bold">
                                        <span class="status-badge status-running">
                                            """ + ("运行中" if self.proxy_service else "未启动") + """
                                        </span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-server fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card metric-card-blue">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">总请求数</div>
                                    <div class="h5 mb-0 font-weight-bold" id="totalRequests">
                                        """ + str(stats.get('summary', {}).get('total_requests', 0)) + """
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card metric-card-green">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">成功率</div>
                                    <div class="h5 mb-0 font-weight-bold" id="successRate">
                                        """ + str(stats.get('summary', {}).get('overall_success_rate', 0)) + """%
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card metric-card-orange">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">平均响应时间</div>
                                    <div class="h5 mb-0 font-weight-bold" id="avgResponseTime">
                                        """ + str(round(stats.get('summary', {}).get('avg_time', 0), 3)) + """s
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card metric-card-purple">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">热重载状态</div>
                                    <div class="h5 mb-0 font-weight-bold" id="hotReloadStatus">
                                        """ + ('启用' if self.enable_hot_reload else '禁用') + """
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-sync-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提供商实例统计 -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-bar"></i> 提供商实例统计
                            </h6>
                            <div class="btn-group">
                                <button class="btn btn-primary btn-sm" data-action="refreshStats">
                                    <i class="fas fa-sync"></i> 刷新统计
                                </button>
                                <button class="btn btn-outline-info btn-sm" data-action="showDetailedStats">
                                    <i class="fas fa-chart-pie"></i> 详细统计
                                </button>
                                <button class="btn btn-outline-success btn-sm" data-action="saveStats">
                                    <i class="fas fa-save"></i> 保存统计
                                </button>
                                <button class="btn btn-outline-danger btn-sm" data-action="resetStats">
                                    <i class="fas fa-trash"></i> 重置统计
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            """ + self._generate_provider_instance_stats(stats) + """
                        </div>
                    </div>
                </div>
            </div>

            """

            response = HTMLResponse(content=dashboard_html)
            response.headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';"
            return response

        @self.app.get("/api/api-info-content")
        async def get_api_info_content(request: Request):
            """获取API信息内容"""
            # 获取服务器信息
            host = request.url.hostname
            port = request.url.port
            scheme = request.url.scheme

            if port and port not in [80, 443]:
                base_url = f"{scheme}://{host}:{port}"
            else:
                base_url = f"{scheme}://{host}"

            # 获取可用模型
            available_models = self.auth_manager.list_public_models()

            api_info_html = f"""
            <!-- 服务地址信息 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-globe"></i> 服务地址</h5>
                        </div>
                        <div class="card-body">
                            <div class="api-endpoint">
                                <h6>基础API地址</h6>
                                <code id="baseUrl">{base_url}</code>
                                <button class="btn btn-sm btn-outline-secondary copy-btn" data-copy-target="baseUrl">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="api-endpoint">
                                        <h6>OpenAI兼容接口</h6>
                                        <code>{base_url}/v1/chat/completions</code>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="api-endpoint">
                                        <h6>通用代理接口</h6>
                                        <code>{base_url}/api/proxy</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 可用模型 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-brain"></i> 可用模型</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>模型名称</th>
                                            <th>描述</th>
                                            <th>提供商</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
            """

            for model in available_models:
                api_info_html += f"""
                                        <tr>
                                            <td><code>{model.get('id', 'Unknown')}</code></td>
                                            <td>{model.get('description', 'No description')}</td>
                                            <td><span class="badge bg-info">{model.get('provider', 'Unknown')}</span></td>
                                            <td><span class="badge bg-success">可用</span></td>
                                        </tr>
                """

            api_info_html += """
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用示例 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-code"></i> 使用示例</h5>
                        </div>
                        <div class="card-body">
                            <h6>cURL 调用示例</h6>
                            <div class="code-block">
<pre id="curlExample">curl -X POST """ + base_url + """/v1/chat/completions \\
  -H "Authorization: Bearer sk-proxy-your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 150
  }'</pre>
                            </div>
                            <button class="btn btn-sm btn-outline-light copy-btn" data-copy-target="curlExample">
                                <i class="fas fa-copy"></i> 复制
                            </button>

                            <h6 class="mt-4">Python (OpenAI SDK) 示例</h6>
                            <div class="code-block">
<pre id="pythonExample">import openai

openai.api_base = \"""" + base_url + """/v1\"
openai.api_key = "sk-proxy-your-api-key"

response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": "Hello!"}]
)

print(response.choices[0].message.content)</pre>
                            </div>
                            <button class="btn btn-sm btn-outline-light copy-btn" data-copy-target="pythonExample">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .api-endpoint {
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 0.375rem;
                    padding: 1rem;
                    margin: 0.5rem 0;
                }
                .code-block {
                    background: #2d3748;
                    color: #e2e8f0;
                    padding: 1rem;
                    border-radius: 0.375rem;
                    overflow-x: auto;
                }
                .copy-btn {
                    position: relative;
                    float: right;
                    margin-top: -2.5rem;
                    margin-right: 0.5rem;
                }
                /* 提供商折叠样式 */
                .cursor-pointer {
                    cursor: pointer;
                }
                .collapse-icon {
                    transition: transform 0.2s ease-in-out;
                }
                .provider-group-header:hover {
                    background-color: rgba(0,0,0,0.02);
                }
                .provider-instance-card {
                    transition: all 0.2s ease-in-out;
                }
                .provider-instance-card:hover {
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }
            </style>


            """

            response = HTMLResponse(content=api_info_html)
            response.headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';"
            return response

        @self.app.get("/api/auth-content")
        async def get_auth_content():
            """获取认证管理内容"""
            auth_html = """
            <!-- API密钥管理 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-key"></i> API密钥管理</h5>
                            <button class="btn btn-primary btn-sm" id="createKeyBtn">
                                <i class="fas fa-plus"></i> 创建密钥
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="apiKeysTable">
                                    <thead>
                                        <tr>
                                            <th>名称</th>
                                            <th>密钥ID</th>
                                            <th>角色</th>
                                            <th>允许模型</th>
                                            <th>速率限制</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模型映射管理 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-sitemap"></i> 模型映射管理</h5>
                            <button class="btn btn-primary btn-sm" id="createMappingBtn">
                                <i class="fas fa-plus"></i> 创建映射
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="modelMappingsTable">
                                    <thead>
                                        <tr>
                                            <th>公开名称</th>
                                            <th>提供商</th>
                                            <th>真实模型</th>
                                            <th>描述</th>
                                            <th>价格倍数</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            """

            response = HTMLResponse(content=auth_html)
            response.headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';"
            return response

        @self.app.get("/api/config-content")
        async def get_config_content():
            """获取配置管理内容"""
            # 获取当前配置
            current_config = self._load_config()

            config_html = f"""
            <!-- 基础配置 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-cog"></i> 基础配置</h5>
                            <button class="btn btn-primary btn-sm" data-action="saveConfig">
                                <i class="fas fa-save"></i> 保存配置
                            </button>
                        </div>
                        <div class="card-body">
                            <form id="configForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">默认超时时间 (秒)</label>
                                            <input type="number" class="form-control" name="default_timeout"
                                                   value="{current_config.get('default_timeout', 30)}" min="1" max="300">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">最大重试次数</label>
                                            <input type="number" class="form-control" name="max_retries"
                                                   value="{current_config.get('max_retries', 3)}" min="0" max="10">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">健康检查间隔 (秒)</label>
                                            <input type="number" class="form-control" name="health_check_interval"
                                                   value="{current_config.get('health_check_interval', 300)}" min="60" max="3600">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">日志级别</label>
                                            <select class="form-select" name="log_level">
                                                <option value="DEBUG" {'selected' if current_config.get('log_level') == 'DEBUG' else ''}>DEBUG</option>
                                                <option value="INFO" {'selected' if current_config.get('log_level') == 'INFO' else ''}>INFO</option>
                                                <option value="WARNING" {'selected' if current_config.get('log_level') == 'WARNING' else ''}>WARNING</option>
                                                <option value="ERROR" {'selected' if current_config.get('log_level') == 'ERROR' else ''}>ERROR</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="enable_monitoring"
                                               {'checked' if current_config.get('enable_monitoring', True) else ''}>
                                        <label class="form-check-label">启用监控</label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提供商配置 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-plug"></i> 提供商配置</h5>
                            <div class="btn-group">
                                <button class="btn btn-outline-secondary btn-sm" data-action="toggleAllProviders" id="toggleAllProvidersBtn">
                                    <i class="fas fa-compress-alt"></i> 全部折叠
                                </button>
                                <button class="btn btn-outline-primary btn-sm" data-action="addProvider" data-provider="openai">
                                    <i class="fas fa-plus"></i> 添加OpenAI实例
                                </button>
                                <button class="btn btn-outline-info btn-sm" data-action="addProvider" data-provider="openrouter">
                                    <i class="fas fa-plus"></i> 添加OpenRouter实例
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="providersContainer">
                                <!-- 动态加载提供商配置 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 服务状态 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle"></i> 服务状态</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            配置文件
                                        </div>
                                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                                            {'存在' if os.path.exists('config.json') else '不存在'}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            热重载状态
                                        </div>
                                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                                            {'启用' if self.enable_hot_reload else '禁用'}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            提供商数量
                                        </div>
                                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                                            {len(current_config.get('providers', {}))} 种类型
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="btn-group w-100" role="group">
                                        <button class="btn btn-outline-success" data-action="testConfiguration">
                                            <i class="fas fa-check"></i> 测试配置
                                        </button>
                                        <button class="btn btn-outline-warning" data-action="reloadConfiguration">
                                            <i class="fas fa-sync"></i> 重载配置
                                        </button>
                                        <button class="btn btn-outline-info" data-action="exportConfiguration">
                                            <i class="fas fa-download"></i> 导出配置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 隐藏的配置数据元素，供JavaScript使用 -->
            <script type="application/json" id="currentConfig">{json.dumps(current_config, ensure_ascii=False)}</script>

            <!-- 初始化配置管理 -->
            <script>
                // 页面加载完成后初始化配置管理
                document.addEventListener('DOMContentLoaded', function() {{
                    if (typeof initConfigManagement === 'function') {{
                        initConfigManagement();
                    }}
                }});
            </script>
            """

            response = HTMLResponse(content=config_html)
            response.headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';"
            return response
    
    def _generate_provider_instance_stats(self, stats: Dict[str, Any]) -> str:
        """生成提供商实例统计HTML

        Args:
            stats: 统计数据

        Returns:
            str: 统计HTML
        """
        if not stats or 'summary' not in stats:
            return """
                <div class="text-center text-muted py-4">
                    <i class="fas fa-chart-line fa-3x mb-3 opacity-50"></i>
                    <p>暂无统计数据</p>
                    <small>开始使用API后将显示详细统计</small>
                </div>
            """

        # 过滤出实例级别的统计（排除summary）
        instance_stats = {k: v for k, v in stats.items() if k != 'summary' and isinstance(v, dict)}

        if not instance_stats:
            return """
                <div class="text-center text-muted py-4">
                    <i class="fas fa-server fa-3x mb-3 opacity-50"></i>
                    <p>暂无实例统计</p>
                    <small>配置提供商实例后将显示统计信息</small>
                </div>
            """

        # 按提供商类型分组
        grouped_stats = {}
        for instance_name, instance_data in instance_stats.items():
            if ':' in instance_name:
                provider_type, instance_id = instance_name.split(':', 1)
            else:
                provider_type = instance_name
                instance_id = 'default'

            if provider_type not in grouped_stats:
                grouped_stats[provider_type] = []

            grouped_stats[provider_type].append({
                'instance_id': instance_id,
                'instance_name': instance_name,
                'data': instance_data
            })

        html_parts = []

        for provider_type, instances in grouped_stats.items():
            provider_name = 'OpenAI' if provider_type == 'openai' else 'OpenRouter' if provider_type == 'openrouter' else provider_type.title()
            icon = 'robot' if provider_type == 'openai' else 'route' if provider_type == 'openrouter' else 'server'
            color = 'primary' if provider_type == 'openai' else 'info' if provider_type == 'openrouter' else 'secondary'

            html_parts.append(f"""
                <div class="mb-4">
                    <h6 class="text-{color} mb-3">
                        <i class="fas fa-{icon}"></i> {provider_name} 提供商 ({len(instances)} 个实例)
                    </h6>
                    <div class="row">
            """)

            for instance in instances:
                data = instance['data']
                success_rate = data.get('success_rate', 0)
                total_requests = data.get('total_requests', 0)
                successful_requests = data.get('successful_requests', 0)
                failed_requests = data.get('failed_requests', 0)
                avg_response_time = data.get('avg_response_time', 0)
                last_request = data.get('last_request', '')
                model_usage = data.get('model_usage', {})
                endpoint_usage = data.get('endpoint_usage', {})

                # 状态颜色
                if success_rate >= 95:
                    status_color = 'success'
                elif success_rate >= 80:
                    status_color = 'warning'
                else:
                    status_color = 'danger'

                # 格式化最后请求时间
                if last_request:
                    try:
                        from datetime import datetime
                        last_time = datetime.fromisoformat(last_request.replace('Z', '+00:00'))
                        last_request_display = last_time.strftime('%m-%d %H:%M')
                    except:
                        last_request_display = '未知'
                else:
                    last_request_display = '从未'

                html_parts.append(f"""
                        <div class="col-lg-6 col-xl-4 mb-3">
                            <div class="card border-left-{status_color}">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="card-title mb-0 text-{color}">
                                            <i class="fas fa-server"></i> {instance['instance_id']}
                                        </h6>
                                        <span class="badge badge-{status_color}">{success_rate}%</span>
                                    </div>

                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="text-xs text-muted">总请求</div>
                                            <div class="font-weight-bold">{total_requests}</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-xs text-muted">成功</div>
                                            <div class="font-weight-bold text-success">{successful_requests}</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-xs text-muted">失败</div>
                                            <div class="font-weight-bold text-danger">{failed_requests}</div>
                                        </div>
                                    </div>

                                    <hr class="my-2">

                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="text-xs text-muted">平均响应</div>
                                            <div class="small">{avg_response_time}s</div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-xs text-muted">最后请求</div>
                                            <div class="small">{last_request_display}</div>
                                        </div>
                                    </div>
                """)

                # 添加模型使用统计
                if model_usage:
                    html_parts.append(f"""
                                    <hr class="my-2">
                                    <div class="text-xs text-muted mb-1">使用的模型:</div>
                                    <div class="d-flex flex-wrap">
                    """)

                    # 按使用次数排序模型
                    sorted_models = sorted(model_usage.items(), key=lambda x: x[1], reverse=True)
                    for model_name, count in sorted_models[:3]:  # 只显示前3个最常用的模型
                        # 简化模型名称显示
                        display_name = model_name.split('/')[-1] if '/' in model_name else model_name
                        if len(display_name) > 15:
                            display_name = display_name[:12] + '...'

                        html_parts.append(f"""
                                        <span class="badge badge-light mr-1 mb-1" title="{model_name}: {count} 次">
                                            {display_name} ({count})
                                        </span>
                        """)

                    if len(model_usage) > 3:
                        html_parts.append(f"""
                                        <span class="badge badge-secondary mr-1 mb-1" title="还有 {len(model_usage) - 3} 个模型">
                                            +{len(model_usage) - 3}
                                        </span>
                        """)

                    html_parts.append("</div>")

                html_parts.append("""
                                </div>
                            </div>
                        </div>
                """)

            html_parts.append("</div></div>")

        return ''.join(html_parts)

    def _get_provider_instance_count(self) -> int:
        """获取提供商实例总数

        Returns:
            int: 提供商实例总数
        """
        if not self.proxy_service:
            return 0

        total_count = 0
        for provider_type, provider_list in self.proxy_service.providers.items():
            total_count += len(provider_list)

        return total_count

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 返回默认配置
                return {
                    "providers": {
                        "openai": [
                            {
                                "name": "primary",
                                "api_key": "sk-your-openai-key-here",
                                "config": {}
                            }
                        ],
                        "openrouter": [
                            {
                                "name": "primary",
                                "api_key": "sk-or-your-openrouter-key-here",
                                "config": {
                                    "timeout": 30,
                                    "site_url": "https://yourapp.com",
                                    "site_name": "Your App Name"
                                }
                            }
                        ]
                    },
                    "default_timeout": 30,
                    "max_retries": 3,
                    "health_check_interval": 300,
                    "enable_monitoring": True,
                    "log_level": "INFO"
                }
        except Exception:
            return {}


def create_app(proxy_service: Optional[ProxyService] = None, enable_hot_reload: bool = True) -> FastAPI:
    """创建Web应用"""
    web_app = WebApp(proxy_service, enable_hot_reload=enable_hot_reload)

    # 启动热重载监控
    if enable_hot_reload:
        web_app.start_hot_reload()

    return web_app.app


def create_app_for_uvicorn() -> FastAPI:
    """为 uvicorn 创建应用实例"""
    # 从配置文件创建代理服务
    config_file = "config.json"
    proxy_service = None

    if os.path.exists(config_file):
        try:
            from .config import Config
            config = Config.from_file(config_file)
            proxy_service = ProxyService(config)
            print(f"✅ 代理服务初始化成功，提供商: {list(config.providers.keys())}")
        except Exception as e:
            print(f"⚠️  加载配置失败: {e}")
    else:
        print(f"⚠️  配置文件不存在: {config_file}")
        print("💡 建议运行: python start_api_service.py --create-config")

        # 创建默认配置
        try:
            from .config import Config
            default_config = Config()
            proxy_service = ProxyService(default_config)
            print("✅ 使用默认配置创建代理服务")
        except Exception as e:
            print(f"❌ 创建默认代理服务失败: {e}")

    return create_app(proxy_service, enable_hot_reload=True)


# 为 uvicorn 导出应用实例
app = create_app_for_uvicorn()
