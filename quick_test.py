#!/usr/bin/env python3
"""
快速测试修复后的流式响应
"""

import requests
import json

def test_quick():
    """快速测试"""
    print("🧪 快速测试流式响应修复...")
    
    # 测试非流式
    print("\n1. 测试非流式响应:")
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "Hi"}],
        "max_tokens": 5,
        "stream": False
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers={"Content-Type": "application/json", "X-Provider": "openrouter"},
            json=test_data,
            timeout=10
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 非流式成功")
        else:
            print(f"   ❌ 非流式失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 非流式异常: {e}")
    
    # 测试流式
    print("\n2. 测试流式响应:")
    test_data["stream"] = True
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers={"Content-Type": "application/json", "X-Provider": "openrouter"},
            json=test_data,
            timeout=10,
            stream=True
        )
        print(f"   状态码: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('Content-Type')}")
        
        if response.status_code == 200:
            print("   ✅ 流式成功")
            # 读取前几行
            count = 0
            for line in response.iter_lines(decode_unicode=True):
                if line and count < 3:
                    print(f"   数据: {line[:100]}...")
                    count += 1
                elif count >= 3:
                    break
        else:
            print(f"   ❌ 流式失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 流式异常: {e}")

if __name__ == "__main__":
    test_quick()
