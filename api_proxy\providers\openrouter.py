# OpenRouter提供商实现
import json
import logging
from typing import Dict, Any, Optional
import requests
import time
from datetime import datetime
from .base import BaseProvider


class OpenRouterProvider(BaseProvider):
    """OpenRouter API提供商实现
    
    特性:
    - 支持多种AI模型的统一API接口
    - 自动重试机制
    - 超时处理
    - 使用时间追踪
    - 支持应用归属标识
    """

    @property
    def name(self) -> str:
        return "openrouter"

    @property
    def last_used(self) -> Optional[datetime]:
        """返回最后API调用时间戳"""
        return self._last_used

    def __init__(self, api_key: str, timeout: int = 30, site_url: str = "", site_name: str = ""):
        """初始化OpenRouter提供者

        Args:
            api_key: OpenRouter API密钥
            timeout: 请求超时(秒)
            site_url: 可选的网站URL，用于OpenRouter排行榜
            site_name: 可选的网站名称，用于OpenRouter排行榜

        Raises:
            ValueError: API密钥无效或超时参数无效
        """
        if not api_key or not isinstance(api_key, str) or not api_key.strip():
            raise ValueError("API密钥必须是非空字符串")
        if not isinstance(timeout, int) or timeout <= 0:
            raise ValueError("超时时间必须是正整数")

        self.api_key = api_key.strip()
        self.timeout = timeout
        self.base_url = "https://openrouter.ai/api/v1"
        self.site_url = site_url.strip() if site_url else ""
        self.site_name = site_name.strip() if site_name else ""
        self.logger = logging.getLogger(__name__)
        self._last_used = None

    def call(self, endpoint: str, max_retries: int = 3, **kwargs) -> Dict[str, Any]:
        """调用OpenRouter API

        Args:
            endpoint: API端点路径
            max_retries: 最大重试次数(1-5)
            **kwargs: API请求参数

        Returns:
            Dict[str, Any]: API响应数据

        Raises:
            ValueError: 参数无效
            requests.HTTPError: API请求失败
            TimeoutError: 请求超时
            RateLimitError: 达到速率限制
            ProviderError: 提供商特定错误
        """
        if not endpoint or not isinstance(endpoint, str):
            raise ValueError("端点路径必须是非空字符串")
        if not 1 <= max_retries <= 5:
            raise ValueError("重试次数必须在1-5之间")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        # 添加可选的应用归属标识头部
        if self.site_url:
            headers["HTTP-Referer"] = self.site_url
        if self.site_name:
            headers["X-Title"] = self.site_name

        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        # 记录请求详情（隐藏敏感信息）
        masked_headers = headers.copy()
        if "Authorization" in masked_headers:
            auth_value = masked_headers["Authorization"]
            if len(auth_value) > 20:
                masked_headers["Authorization"] = f"{auth_value[:20]}***"

        # 强制使用ERROR级别确保日志显示
        self.logger.error(f"🚀 OpenRouter API请求开始:")
        self.logger.error(f"   URL: {url}")
        self.logger.error(f"   Headers: {masked_headers}")
        self.logger.error(f"   Payload keys: {list(kwargs.keys())}")
        if 'model' in kwargs:
            self.logger.error(f"   Model: {kwargs['model']}")

        # 记录完整请求体（隐藏敏感信息）
        safe_payload = kwargs.copy()
        if 'messages' in safe_payload and len(safe_payload['messages']) > 0:
            # 只显示第一条消息的前100个字符
            first_msg = safe_payload['messages'][0]
            if 'content' in first_msg and len(first_msg['content']) > 100:
                safe_payload['messages'][0] = {
                    **first_msg,
                    'content': first_msg['content'][:100] + '...'
                }
        self.logger.error(f"   完整请求体: {json.dumps(safe_payload, ensure_ascii=False, indent=2)}")

        for attempt in range(max_retries):
            try:
                self.logger.error(f"📡 发送请求 (尝试 {attempt + 1}/{max_retries})...")
                response = requests.post(url, headers=headers, json=kwargs, timeout=self.timeout)

                # 记录响应详情 - 使用ERROR级别确保显示
                self.logger.error(f"📥 收到响应:")
                self.logger.error(f"   状态码: {response.status_code}")
                self.logger.error(f"   响应头: {dict(response.headers)}")
                self.logger.error(f"   响应大小: {len(response.content)} bytes")

                # 尝试获取响应文本（用于调试）
                try:
                    response_text = response.text
                    if len(response_text) > 1000:
                        self.logger.error(f"   响应内容预览: {response_text[:1000]}...")
                    else:
                        self.logger.error(f"   响应内容完整: {response_text}")
                except Exception as text_error:
                    self.logger.error(f"   无法读取响应文本: {text_error}")

                # 检查HTTP状态码
                response.raise_for_status()

                # 尝试解析JSON
                try:
                    json_data = response.json()
                    self.logger.error(f"✅ JSON解析成功")
                    self.logger.error(f"   解析后数据类型: {type(json_data)}")
                    if isinstance(json_data, dict):
                        self.logger.error(f"   响应数据键: {list(json_data.keys())}")
                        if 'choices' in json_data:
                            self.logger.error(f"   choices数量: {len(json_data['choices'])}")
                        if 'usage' in json_data:
                            self.logger.error(f"   token使用情况: {json_data['usage']}")
                    self._last_used = datetime.now()
                    return json_data
                except ValueError as json_error:
                    self.logger.error(f"❌ JSON解析失败: {json_error}")
                    self.logger.error(f"   原始响应: {response.text}")
                    raise ValueError(f"JSON解析失败: {json_error}") from json_error

            except requests.Timeout as e:
                self.logger.error(f"⏰ 请求超时 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    self.logger.error("❌ 达到最大重试次数，请求超时")
                    raise
            except requests.ConnectionError as e:
                self.logger.error(f"🔌 连接错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    self.logger.error("❌ 达到最大重试次数，连接失败")
                    raise
            except requests.HTTPError as e:
                self.logger.error(f"🚫 HTTP错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                self.logger.error(f"   状态码: {e.response.status_code if e.response else 'Unknown'}")
                if e.response:
                    self.logger.error(f"   错误响应: {e.response.text}")
                if attempt == max_retries - 1:
                    self.logger.error("❌ 达到最大重试次数，HTTP错误")
                    raise
            except requests.RequestException as e:
                self.logger.error(f"🔥 请求异常 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                self.logger.error(f"   异常类型: {type(e).__name__}")
                if hasattr(e, 'response') and e.response:
                    self.logger.error(f"   响应状态码: {e.response.status_code}")
                    self.logger.error(f"   响应内容: {e.response.text}")
                if attempt == max_retries - 1:
                    self.logger.error("❌ 达到最大重试次数，请求异常")
                    raise
            except Exception as e:
                self.logger.error(f"💥 未知错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                self.logger.error(f"   错误类型: {type(e).__name__}")
                if attempt == max_retries - 1:
                    self.logger.error("❌ 达到最大重试次数，未知错误")
                    raise

            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries - 1:
                wait_time = 2**attempt
                self.logger.info(f"⏳ 等待 {wait_time}秒后重试...")
                time.sleep(wait_time)

    def __str__(self) -> str:
        """安全的字符串表示，隐藏API密钥"""
        masked_key = f"{self.api_key[:8]}***" if len(self.api_key) > 8 else "***"
        return f"OpenRouterProvider(api_key={masked_key}, timeout={self.timeout}, site_name={self.site_name})"

    def __repr__(self) -> str:
        """安全的对象表示"""
        return self.__str__()
