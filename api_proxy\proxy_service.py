"""API代理服务核心逻辑

提供以下主要功能:
- 多提供商负载均衡(Round-robin)
- 自动故障转移和重试
- 请求监控和统计
- 健康检查和状态管理
- 作业失败智能分析

使用示例:
    config = Config({
        "openai": [
            ProviderConfig("account1", "sk-xxx1"),
            ProviderConfig("account2", "sk-xxx2")
        ]
    })
    service = ProxyService(config)
    response = service.call("openai", "chat/completions",
                         messages=[{"role":"user","content":"hello"}])
"""

import logging
import time
import threading
from typing import Dict, List, Optional, Any
from .providers.base import BaseProvider
from .config import Config
from .utils import get_round_robin_provider
from .models import ProviderConfig
from .monitoring import RequestMonitor
from .health_check import HealthChecker

from enum import Enum, auto
from dataclasses import dataclass
from datetime import datetime
import re

from .job_analysis import JobErrorType, JobFailureAnalysis, JobAnalyzer


class ProxyService:
    """统一API代理服务,支持多提供商轮换

    主要功能:
    - 多供应商负载均衡
    - 自动故障转移
    - 请求监控
    - 健康检查
    - 作业失败分析
    """

    def __init__(self, config: Config):
        """初始化代理服务

        Args:
            config (Config): 服务配置

        Raises:
            ValueError: 如果配置无效
        """
        if not isinstance(config, Config):
            raise ValueError("无效的配置类型")

        self.config = config
        self.providers: Dict[str, List[BaseProvider]] = {}
        self.monitor = RequestMonitor()
        self.health_checker = HealthChecker(
            interval=60, timeout=10, max_retries=3  # 默认60秒检查一次  # 10秒超时
        )
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()  # 用于线程安全的热重载
        self._init_providers()

    def _init_providers(self):
        """从配置初始化提供商"""
        with self._lock:
            for provider_type, provider_configs in self.config.providers.items():
                self.providers[provider_type] = [
                    self._create_provider(provider_type, config)
                    for config in provider_configs
                ]

    def register_provider(self, provider_type: str, provider: BaseProvider):
        """注册新的API提供商"""
        if provider_type not in self.providers:
            self.providers[provider_type] = []
        self.providers[provider_type].append(provider)

    def analyze_job_failure(self, log_text: str) -> List[JobFailureAnalysis]:
        """分析作业失败日志

        使用JobAnalyzer进行日志分析并返回结果

        Args:
            log_text: 作业日志文本

        Returns:
            包含所有错误分析结果列表
        """
        analyzer = JobAnalyzer()
        return analyzer.analyze_log(log_text)

    def _create_provider(self, provider_type: str, config: ProviderConfig) -> BaseProvider:
        """根据配置创建提供商实例

        Args:
            provider_type: 提供商类型 (如 'openai')
            config: 提供商配置

        Returns:
            BaseProvider: 创建的提供商实例

        Raises:
            ValueError: 不支持的提供商类型
        """
        if provider_type == "openai":
            from .providers.openai import OpenAIProvider
            return OpenAIProvider(config.api_key)
        elif provider_type == "openrouter":
            from .providers.openrouter import OpenRouterProvider
            # 从配置中获取可选参数
            timeout = config.config.get("timeout", 30)
            site_url = config.config.get("site_url", "")
            site_name = config.config.get("site_name", "")
            return OpenRouterProvider(config.api_key, timeout=timeout, site_url=site_url, site_name=site_name)
        else:
            raise ValueError(f"不支持的提供商类型: {provider_type}")

    def get_provider(self, provider_type: str) -> BaseProvider:
        """获取当前轮询到的API提供商

        Args:
            provider_type: 提供商类型

        Returns:
            BaseProvider: 轮询到的提供商实例

        Raises:
            ValueError: 提供商类型不存在或无可用提供商
        """
        if provider_type not in self.providers:
            raise ValueError(f"提供商类型 '{provider_type}' 不存在")

        providers = self.providers[provider_type]
        if not providers:
            raise ValueError(f"提供商类型 '{provider_type}' 无可用实例")

        return get_round_robin_provider(providers)

    def _get_provider_instance_name(self, provider_type: str, provider_instance: BaseProvider) -> str:
        """获取提供商实例的唯一名称

        Args:
            provider_type: 提供商类型
            provider_instance: 提供商实例

        Returns:
            str: 实例的唯一名称，格式为 "provider_type:instance_name"
        """
        # 在提供商列表中找到这个实例的索引和配置名称
        providers = self.providers.get(provider_type, [])
        provider_configs = self.config.providers.get(provider_type, [])

        for i, provider in enumerate(providers):
            if provider is provider_instance:
                if i < len(provider_configs):
                    instance_name = provider_configs[i].name
                    return f"{provider_type}:{instance_name}"
                else:
                    return f"{provider_type}:instance_{i}"

        # 如果找不到，使用对象ID作为后备
        return f"{provider_type}:unknown_{id(provider_instance)}"

    def call(self, provider_type: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """调用指定提供商的API

        Args:
            provider_type: 提供商类型 (如 'openai')
            endpoint: API端点路径 (如 'chat/completions')
            **kwargs: API请求参数

        Returns:
            Dict[str, Any]: API响应数据

        Raises:
            ValueError: 参数无效或提供商不存在
            Exception: API调用失败
        """
        if not provider_type or not isinstance(provider_type, str):
            raise ValueError("提供商类型必须是非空字符串")
        if not endpoint or not isinstance(endpoint, str):
            raise ValueError("端点路径必须是非空字符串")

        provider = self.get_provider(provider_type)

        # 获取提供商实例的详细信息
        provider_instance_name = self._get_provider_instance_name(provider_type, provider)

        # 提取模型信息（如果存在）
        model = kwargs.get('model', None)

        # 记录请求开始时间
        start_time = time.time()
        success = False
        error_type = None

        try:
            self.logger.error(f"🎯 代理服务调用: {provider_instance_name}/{endpoint}")
            if model:
                self.logger.error(f"   模型: {model}")
            self.logger.error(f"   请求参数键: {list(kwargs.keys())}")

            response = provider.call(endpoint, **kwargs)
            success = True
            self.logger.error(f"✅ API调用成功: {provider_instance_name}/{endpoint}")
            self.logger.error(f"   响应类型: {type(response)}")
            if isinstance(response, dict):
                self.logger.error(f"   响应键: {list(response.keys())}")
            return response
        except Exception as e:
            error_type = type(e).__name__
            self.logger.error(f"❌ API调用失败: {provider_instance_name}/{endpoint}")
            self.logger.error(f"   错误类型: {error_type}")
            self.logger.error(f"   错误信息: {str(e)}")
            self.logger.error(f"   提供商: {provider}")

            # 记录更多调试信息
            import traceback
            self.logger.error(f"   完整堆栈跟踪:")
            for line in traceback.format_exc().split('\n'):
                if line.strip():
                    self.logger.error(f"     {line}")
            raise
        finally:
            # 记录请求统计 - 使用实例级别的名称，包含模型和端点信息
            duration = time.time() - start_time
            self.monitor.record_request(provider_instance_name, duration, success, error_type, model, endpoint)

    def hot_reload(self, new_config: Config):
        """热重载服务配置

        Args:
            new_config: 新的配置对象

        Raises:
            ValueError: 如果新配置无效
        """
        if not isinstance(new_config, Config):
            raise ValueError("无效的配置类型")

        with self._lock:
            try:
                self.logger.info("开始热重载服务配置...")

                # 保存旧配置和提供商
                old_config = self.config
                old_providers = self.providers.copy()

                # 更新配置
                self.config = new_config

                # 重新初始化提供商
                self.providers.clear()
                self._init_providers()

                # 更新健康检查器配置
                self.health_checker.interval = new_config.health_check_interval

                # 更新日志级别
                if old_config.log_level != new_config.log_level:
                    logging.getLogger().setLevel(getattr(logging, new_config.log_level.upper()))

                self.logger.info("服务配置热重载成功")

                # 清理旧提供商资源（如果需要）
                self._cleanup_old_providers(old_providers)

            except Exception as e:
                self.logger.error(f"热重载失败，回滚到旧配置: {e}")
                # 回滚配置
                self.config = old_config
                self.providers = old_providers
                raise

    def _cleanup_old_providers(self, old_providers: Dict[str, List[BaseProvider]]):
        """清理旧的提供商资源"""
        try:
            for provider_type, providers in old_providers.items():
                for provider in providers:
                    if hasattr(provider, 'cleanup'):
                        try:
                            provider.cleanup()
                        except Exception as e:
                            self.logger.warning(f"清理提供商 {provider_type} 资源失败: {e}")
        except Exception as e:
            self.logger.warning(f"清理旧提供商资源时出错: {e}")

    def get_provider_safely(self, provider_type: str) -> Optional[BaseProvider]:
        """线程安全地获取提供商"""
        with self._lock:
            try:
                return self.get_provider(provider_type)
            except ValueError:
                return None

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态信息"""
        with self._lock:
            provider_status = {}
            for provider_type, providers in self.providers.items():
                provider_status[provider_type] = {
                    "count": len(providers),
                    "instances": [
                        {
                            "name": getattr(provider, 'name', 'unknown'),
                            "last_used": getattr(provider, 'last_used', None)
                        }
                        for provider in providers
                    ]
                }

            return {
                "config": {
                    "provider_types": list(self.config.providers.keys()),
                    "total_providers": sum(len(providers) for providers in self.providers.values()),
                    "default_timeout": self.config.default_timeout,
                    "max_retries": self.config.max_retries,
                    "monitoring_enabled": self.config.enable_monitoring,
                    "log_level": self.config.log_level
                },
                "providers": provider_status,
                "monitoring": self.monitor.get_stats() if self.config.enable_monitoring else None,
                "health_check_interval": self.health_checker.interval
            }

    def cleanup(self):
        """清理服务资源"""
        with self._lock:
            self.logger.info("正在清理服务资源...")

            # 保存监控统计数据
            if hasattr(self.monitor, 'cleanup'):
                self.monitor.cleanup()

            self._cleanup_old_providers(self.providers)
            self.providers.clear()
            self.logger.info("服务资源清理完成")
